import 'dart:io';

import 'package:flutter/foundation.dart';

class ApiConstants {
  // Base URL for the API
  // Use your computer's IP address instead of localhost for mobile access
  static final baseUrl = kIsWeb || Platform.isMacOS || Platform.isIOS ? 'http://127.0.0.1:3000' : 'http://********:3000';

  // Auth endpoints
  static const String registerEndpoint = '/users/register';
  static const String loginEndpoint = '/users/login';

  // User endpoints
  static const String userStatisticsEndpoint = '/users/me/statistics';
  static const String userUpdateEndpoint = '/users/update';

  // Donations endpoints
  static const String userDonationsPaginatedEndpoint = '/donations/user/me/paginated';

  // Campaigns endpoints
  static const String activeCampaignsEndpoint = '/campaigns/active';
  static const String createCampaignEndpoint = '/campaigns/create';
  static const String updateCampaignEndpoint = '/campaigns'; // Will append /{id}

  // Payment endpoints
  static const String createPaymentEndpoint = '/payments/create';

  // External payment URLs
  static const String eftaaPayCheckoutBaseUrl = 'https://api.cloud.preprod.eftaapay.com/online/public/checkout';
  static const String paymentReturnBaseUrl = 'https://98eac77d7154.ngrok-free.app/payments/checkout-status';
  static const String successReturnUrl = 'https://hazelsone.com/return?status=success';
  static const String failedReturnUrl = 'https://hazelsone.com/return?status=failed';

  // Full URLs
  static String get registerUrl => '$baseUrl$registerEndpoint';
  static String get loginUrl => '$baseUrl$loginEndpoint';
  static String get userStatisticsUrl => '$baseUrl$userStatisticsEndpoint';
  static String get userUpdateUrl => '$baseUrl$userUpdateEndpoint';
  static String get userDonationsPaginatedUrl => '$baseUrl$userDonationsPaginatedEndpoint';
  static String get activeCampaignsUrl => '$baseUrl$activeCampaignsEndpoint';
  static String get createCampaignUrl => '$baseUrl$createCampaignEndpoint';
  static String get createPaymentUrl => '$baseUrl$createPaymentEndpoint';

  // Campaign URL builders
  static String getUpdateCampaignUrl(String campaignId) => '$baseUrl$updateCampaignEndpoint/$campaignId';

  // Payment checkout URL builder
  static String getCheckoutUrl(String transactionId) =>
      '$eftaaPayCheckoutBaseUrl?transactionId=$transactionId';
}
