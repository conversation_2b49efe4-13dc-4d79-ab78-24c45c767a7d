import 'package:dio/dio.dart';
import '../constants/api_constants.dart';
import '../models/api_response.dart';
import 'api_service.dart';
import 'token_service.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final ApiService _apiService = ApiService();

  /// Register a new user
  Future<ApiResponse> register({
    required String name,
    required String email,
    required String password,
  }) async {
    try {
      final response = await _apiService.post(
        ApiConstants.registerEndpoint,
        data: {
          'name': name,
          'email': email,
          'password': password,
        },
      );

      final apiResponse = ApiResponse.fromJson(response.data);

      // Store token and user data if registration successful
      if (apiResponse.isSuccess && apiResponse.data != null) {
        final data = apiResponse.data as Map<String, dynamic>;
        if (data.contains<PERSON>ey('token') && data.contains<PERSON>ey('user')) {
          await TokenService.storeAuthData(
            token: data['token'],
            userData: data['user'],
          );
        }
      }

      return apiResponse;
    } on DioException catch (e) {
      return _handleDioError(e);
    } catch (e) {
      return ApiResponse(
        status: false,
        message: 'An unexpected error occurred. Please try again.',
        data: null,
      );
    }
  }

  /// Login user
  Future<ApiResponse> login({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _apiService.post(
        ApiConstants.loginEndpoint,
        data: {
          'email': email,
          'password': password,
        },
      );

      final apiResponse = ApiResponse.fromJson(response.data);

      // Store token and user data if login successful
      if (apiResponse.isSuccess && apiResponse.data != null) {
        final data = apiResponse.data as Map<String, dynamic>;
        if (data.containsKey('token') && data.containsKey('user')) {
          await TokenService.storeAuthData(
            token: data['token'],
            userData: data['user'],
          );
        }
      }

      return apiResponse;
    } on DioException catch (e) {
      return _handleDioError(e);
    } catch (e) {
      return ApiResponse(
        status: false,
        message: 'An unexpected error occurred. Please try again.',
        data: null,
      );
    }
  }
  
  /// Logout user
  Future<void> logout() async {
    await TokenService.clearAuthData();
  }
  
  /// Check if user is authenticated
  Future<bool> isAuthenticated() async {
    return await TokenService.isTokenValid();
  }
  
  /// Get current user data
  Future<Map<String, dynamic>?> getCurrentUser() async {
    return await TokenService.getUserData();
  }

  /// Get current user ID
  Future<String?> getUserId() async {
    final userData = await getCurrentUser();
    return userData?['id'] as String?;
  }

  /// Handle Dio errors and extract meaningful error messages
  ApiResponse _handleDioError(DioException e) {
    String errorMessage = 'An unexpected error occurred. Please try again.';

    if (e.response != null) {
      final responseData = e.response!.data;

      // Try to extract error message from API response
      if (responseData is Map<String, dynamic>) {
        if (responseData.containsKey('message')) {
          errorMessage = responseData['message'];
        } else if (responseData.containsKey('error')) {
          errorMessage = responseData['error'];
        } else {
          // Handle different status codes
          switch (e.response!.statusCode) {
            case 400:
              errorMessage = 'Invalid request. Please check your input.';
              break;
            case 401:
              errorMessage = 'Invalid credentials. Please check your email and password.';
              break;
            case 403:
              errorMessage = 'Access denied. Please contact support.';
              break;
            case 404:
              errorMessage = 'Service not found. Please try again later.';
              break;
            case 409:
              errorMessage = 'Email already exists. Please use a different email.';
              break;
            case 422:
              errorMessage = 'Invalid data provided. Please check your input.';
              break;
            case 500:
              errorMessage = 'Server error. Please try again later.';
              break;
            default:
              errorMessage = 'Server error (${e.response!.statusCode}). Please try again.';
          }
        }
      }
    } else {
      // Handle network errors
      switch (e.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          errorMessage = 'Connection timeout. Please check your internet connection.';
          break;
        case DioExceptionType.connectionError:
          errorMessage = 'Connection failed. Please check your internet connection.';
          break;
        case DioExceptionType.cancel:
          errorMessage = 'Request was cancelled.';
          break;
        default:
          errorMessage = 'Network error. Please check your connection and try again.';
      }
    }

    return ApiResponse(
      status: false,
      message: errorMessage,
      data: null,
    );
  }
}
