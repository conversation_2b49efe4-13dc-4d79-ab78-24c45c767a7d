import '../constants/api_constants.dart';
import '../models/api_response.dart';
import 'api_service.dart';
import 'token_service.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final ApiService _apiService = ApiService();

  /// Register a new user
  Future<ApiResponse> register({
    required String name,
    required String email,
    required String password,
  }) async {
    try {
      final response = await _apiService.post(
        ApiConstants.registerEndpoint,
        data: {
          'name': name,
          'email': email,
          'password': password,
        },
      );

      final apiResponse = ApiResponse.fromJson(response.data);
      
      // Store token and user data if registration successful
      if (apiResponse.isSuccess && apiResponse.data != null) {
        final data = apiResponse.data as Map<String, dynamic>;
        if (data.containsKey('token') && data.containsKey('user')) {
          await TokenService.storeAuthData(
            token: data['token'],
            userData: data['user'],
          );
        }
      }

      return apiResponse;
    } catch (e) {
      return ApiResponse(
        status: false,
        message: e.toString(),
        data: null,
      );
    }
  }

  /// Login user
  Future<ApiResponse> login({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _apiService.post(
        ApiConstants.loginEndpoint,
        data: {
          'email': email,
          'password': password,
        },
      );

      final apiResponse = ApiResponse.fromJson(response.data);
      
      // Store token and user data if login successful
      if (apiResponse.isSuccess && apiResponse.data != null) {
        final data = apiResponse.data as Map<String, dynamic>;
        if (data.containsKey('token') && data.containsKey('user')) {
          await TokenService.storeAuthData(
            token: data['token'],
            userData: data['user'],
          );
        }
      }

      return apiResponse;
    } catch (e) {
      return ApiResponse(
        status: false,
        message: e.toString(),
        data: null,
      );
    }
  }
  
  /// Logout user
  Future<void> logout() async {
    await TokenService.clearAuthData();
  }
  
  /// Check if user is authenticated
  Future<bool> isAuthenticated() async {
    return await TokenService.isTokenValid();
  }
  
  /// Get current user data
  Future<Map<String, dynamic>?> getCurrentUser() async {
    return await TokenService.getUserData();
  }

  /// Get current user ID
  Future<String?> getUserId() async {
    final userData = await getCurrentUser();
    return userData?['id'] as String?;
  }
}
