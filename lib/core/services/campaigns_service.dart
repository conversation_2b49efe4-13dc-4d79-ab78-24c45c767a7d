import 'package:dio/dio.dart';
import '../constants/api_constants.dart';
import '../models/api_response.dart';
import '../models/campaigns_paginated.dart';
import '../models/campaign.dart';
import 'api_service.dart';
import 'auth_service.dart';

class CampaignsService {
  static final CampaignsService _instance = CampaignsService._internal();
  factory CampaignsService() => _instance;
  CampaignsService._internal();

  final ApiService _apiService = ApiService();
  final AuthService _authService = AuthService();

  /// Get active campaigns with pagination
  Future<ApiResponse<CampaignsPaginated>> getActiveCampaigns({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiService.get(
        ApiConstants.activeCampaignsEndpoint,
        queryParameters: {
          'page': page,
          'limit': limit,
        },
      );
      
      // The API returns the data directly without wrapping in a standard ApiResponse
      // So we need to handle it differently
      final campaignsPaginated = CampaignsPaginated.fromJson(response.data);
      
      return ApiResponse<CampaignsPaginated>(
        status: true,
        message: 'Campaigns retrieved successfully',
        data: campaignsPaginated,
      );
    } on DioException catch (e) {
      return ApiResponse<CampaignsPaginated>(
        status: false,
        message: _handleDioError(e),
        data: null,
      );
    } catch (e) {
      return ApiResponse<CampaignsPaginated>(
        status: false,
        message: 'An unexpected error occurred. Please try again.',
        data: null,
      );
    }
  }

  /// Get featured campaigns (limited to 4 for home page)
  Future<ApiResponse<CampaignsPaginated>> getFeaturedCampaigns({
    int limit = 4,
  }) async {
    return getActiveCampaigns(page: 1, limit: limit);
  }

  /// Create a new campaign
  Future<ApiResponse<void>> createCampaign({
    required String title,
    required String description,
    required double goalAmount,
    required DateTime startDate,
    required DateTime endDate,
    String? imageUrl,
  }) async {
    try {
      // Get user ID from session storage
      final userId = await _authService.getUserId();
      if (userId == null) {
        return ApiResponse<void>(
          status: false,
          message: 'User not authenticated',
          data: null,
        );
      }

      final body = {
        'title': title,
        'description': description,
        'goalAmount': goalAmount, // Keep as decimal, API expects decimal values
        'startDate': startDate.toIso8601String(),
        'endDate': endDate.toIso8601String(),
        'userId': userId,
      };

      // Add imageUrl only if provided
      if (imageUrl != null && imageUrl.isNotEmpty) {
        body['imageUrl'] = imageUrl;
      }

      final response = await _apiService.post(
        ApiConstants.createCampaignEndpoint,
        data: body,
      );

      return ApiResponse<void>(
        status: response.data['status'] ?? true,
        message: response.data['message'] ?? 'Campaign created successfully',
        data: null,
      );
    } on DioException catch (e) {
      return ApiResponse<void>(
        status: false,
        message: _handleDioError(e),
        data: null,
      );
    } catch (e) {
      return ApiResponse<void>(
        status: false,
        message: 'An unexpected error occurred. Please try again.',
        data: null,
      );
    }
  }

  /// Update an existing campaign
  Future<ApiResponse<Campaign>> updateCampaign({
    required String campaignId,
    String? title,
    String? description,
    double? goalAmount,
    DateTime? endDate,
    String? imageUrl,
  }) async {
    try {
      final body = <String, dynamic>{};

      // Add only provided fields to the body
      if (title != null) body['title'] = title;
      if (description != null) body['description'] = description;
      if (goalAmount != null) body['goalAmount'] = goalAmount; // Keep as decimal, API expects decimal values
      if (endDate != null) body['endDate'] = endDate.toIso8601String();
      if (imageUrl != null && imageUrl.isNotEmpty) body['imageUrl'] = imageUrl;

      final response = await _apiService.patch(
        '${ApiConstants.updateCampaignEndpoint}/$campaignId',
        data: body,
      );

      print('Update campaign response status: ${response.statusCode}');
      print('Update campaign response data: ${response.data}');

      // Handle the API response properly
      if (response.data != null) {
        try {
          // The API returns the updated campaign data directly
          final updatedCampaign = Campaign.fromJson(response.data);

          return ApiResponse<Campaign>(
            status: true,
            message: 'Campaign updated successfully',
            data: updatedCampaign,
          );
        } catch (parseError) {
          // If parsing fails, still return success since the update worked
          print('Campaign update successful but parsing failed: $parseError');
          return ApiResponse<Campaign>(
            status: true,
            message: 'Campaign updated successfully',
            data: null,
          );
        }
      } else {
        return ApiResponse<Campaign>(
          status: true,
          message: 'Campaign updated successfully',
          data: null,
        );
      }
    } on DioException catch (e) {
      print('Update campaign DioException: $e');
      return ApiResponse<Campaign>(
        status: false,
        message: _handleDioError(e),
        data: null,
      );
    } catch (e) {
      print('Update campaign error: $e');
      return ApiResponse<Campaign>(
        status: false,
        message: 'An unexpected error occurred. Please try again.',
        data: null,
      );
    }
  }

  /// Handle Dio errors and extract meaningful error messages
  String _handleDioError(DioException e) {
    String errorMessage = 'An unexpected error occurred. Please try again.';

    if (e.response != null) {
      final responseData = e.response!.data;

      // Try to extract error message from API response
      if (responseData is Map<String, dynamic>) {
        if (responseData.containsKey('message')) {
          errorMessage = responseData['message'];
        } else if (responseData.containsKey('error')) {
          errorMessage = responseData['error'];
        } else {
          // Handle different status codes
          switch (e.response!.statusCode) {
            case 400:
              errorMessage = 'Invalid request. Please check your input.';
              break;
            case 401:
              errorMessage = 'Authentication required. Please log in again.';
              break;
            case 403:
              errorMessage = 'Access denied. You don\'t have permission to perform this action.';
              break;
            case 404:
              errorMessage = 'Campaign not found.';
              break;
            case 422:
              errorMessage = 'Invalid data provided. Please check your input.';
              break;
            case 500:
              errorMessage = 'Server error. Please try again later.';
              break;
            default:
              errorMessage = 'Server error (${e.response!.statusCode}). Please try again.';
          }
        }
      }
    } else {
      // Handle network errors
      switch (e.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          errorMessage = 'Connection timeout. Please check your internet connection.';
          break;
        case DioExceptionType.connectionError:
          errorMessage = 'Connection failed. Please check your internet connection.';
          break;
        case DioExceptionType.cancel:
          errorMessage = 'Request was cancelled.';
          break;
        default:
          errorMessage = 'Network error. Please check your connection and try again.';
      }
    }

    return errorMessage;
  }
}
