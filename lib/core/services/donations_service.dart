import 'package:dio/dio.dart';
import '../constants/api_constants.dart';
import '../models/api_response.dart';
import '../models/transaction.dart';
import 'api_service.dart';

class DonationsService {
  static final DonationsService _instance = DonationsService._internal();
  factory DonationsService() => _instance;
  DonationsService._internal();

  final ApiService _apiService = ApiService();

  /// Get paginated user donations
  Future<ApiResponse<TransactionsPaginated>> getUserDonationsPaginated({
    int page = 1,
    int limit = 10,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParameters = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      // Add date filters if provided
      if (startDate != null) {
        queryParameters['startDate'] = startDate.toIso8601String().split('T')[0];
      }
      if (endDate != null) {
        queryParameters['endDate'] = endDate.toIso8601String().split('T')[0];
      }

      final response = await _apiService.get(
        ApiConstants.userDonationsPaginatedEndpoint,
        queryParameters: queryParameters,
      );

      // The API returns the data directly without wrapping in a standard ApiResponse
      // So we need to handle it differently
      final transactionsPaginated = TransactionsPaginated.fromJson(response.data);

      return ApiResponse<TransactionsPaginated>(
        status: true,
        message: 'Transactions retrieved successfully',
        data: transactionsPaginated,
      );
    } on DioException catch (e) {
      return ApiResponse<TransactionsPaginated>(
        status: false,
        message: _handleDioError(e),
        data: null,
      );
    } catch (e) {
      return ApiResponse<TransactionsPaginated>(
        status: false,
        message: 'An unexpected error occurred. Please try again.',
        data: null,
      );
    }
  }

  /// Handle Dio errors and extract meaningful error messages
  String _handleDioError(DioException e) {
    String errorMessage = 'An unexpected error occurred. Please try again.';

    if (e.response != null) {
      final responseData = e.response!.data;

      // Try to extract error message from API response
      if (responseData is Map<String, dynamic>) {
        if (responseData.containsKey('message')) {
          errorMessage = responseData['message'];
        } else if (responseData.containsKey('error')) {
          errorMessage = responseData['error'];
        } else {
          // Handle different status codes
          switch (e.response!.statusCode) {
            case 400:
              errorMessage = 'Invalid request. Please check your input.';
              break;
            case 401:
              errorMessage = 'Authentication required. Please log in again.';
              break;
            case 403:
              errorMessage = 'Access denied. You don\'t have permission to view this data.';
              break;
            case 404:
              errorMessage = 'Data not found.';
              break;
            case 500:
              errorMessage = 'Server error. Please try again later.';
              break;
            default:
              errorMessage = 'Server error (${e.response!.statusCode}). Please try again.';
          }
        }
      }
    } else {
      // Handle network errors
      switch (e.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          errorMessage = 'Connection timeout. Please check your internet connection.';
          break;
        case DioExceptionType.connectionError:
          errorMessage = 'Connection failed. Please check your internet connection.';
          break;
        case DioExceptionType.cancel:
          errorMessage = 'Request was cancelled.';
          break;
        default:
          errorMessage = 'Network error. Please check your connection and try again.';
      }
    }

    return errorMessage;
  }
}
