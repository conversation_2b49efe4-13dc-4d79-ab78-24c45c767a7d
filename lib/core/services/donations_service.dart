import '../constants/api_constants.dart';
import '../models/api_response.dart';
import '../models/transaction.dart';
import 'api_service.dart';

class DonationsService {
  static final DonationsService _instance = DonationsService._internal();
  factory DonationsService() => _instance;
  DonationsService._internal();

  final ApiService _apiService = ApiService();

  /// Get paginated user donations
  Future<ApiResponse<TransactionsPaginated>> getUserDonationsPaginated({
    int page = 1,
    int limit = 10,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParameters = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      // Add date filters if provided
      if (startDate != null) {
        queryParameters['startDate'] = startDate.toIso8601String().split('T')[0];
      }
      if (endDate != null) {
        queryParameters['endDate'] = endDate.toIso8601String().split('T')[0];
      }

      final response = await _apiService.get(
        ApiConstants.userDonationsPaginatedEndpoint,
        queryParameters: queryParameters,
      );

      // The API returns the data directly without wrapping in a standard ApiResponse
      // So we need to handle it differently
      final transactionsPaginated = TransactionsPaginated.fromJson(response.data);

      return ApiResponse<TransactionsPaginated>(
        status: true,
        message: 'Transactions retrieved successfully',
        data: transactionsPaginated,
      );
    } catch (e) {
      return ApiResponse<TransactionsPaginated>(
        status: false,
        message: 'Failed to load transactions: ${e.toString()}',
        data: null,
      );
    }
  }
}
