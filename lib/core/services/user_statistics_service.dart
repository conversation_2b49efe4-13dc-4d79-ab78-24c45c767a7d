import '../constants/api_constants.dart';
import '../models/api_response.dart';
import '../models/user_statistics.dart';
import 'api_service.dart';

class UserStatisticsService {
  static final UserStatisticsService _instance = UserStatisticsService._internal();
  factory UserStatisticsService() => _instance;
  UserStatisticsService._internal();

  final ApiService _apiService = ApiService();

  /// Get user statistics including donations, charities, and events
  Future<ApiResponse<UserStatistics>> getUserStatistics() async {
    try {
      final response = await _apiService.get(ApiConstants.userStatisticsEndpoint);
      
      final apiResponse = ApiResponse.fromJson(response.data);
      
      if (apiResponse.isSuccess && apiResponse.data != null) {
        final userStatistics = UserStatistics.fromJson(apiResponse.data);
        return ApiResponse<UserStatistics>(
          status: apiResponse.status,
          message: apiResponse.message,
          data: userStatistics,
        );
      } else {
        return ApiResponse<UserStatistics>(
          status: apiResponse.status,
          message: apiResponse.message,
          data: null,
        );
      }
    } catch (e) {
      return ApiResponse<UserStatistics>(
        status: false,
        message: 'Failed to load user statistics: ${e.toString()}',
        data: null,
      );
    }
  }
}
