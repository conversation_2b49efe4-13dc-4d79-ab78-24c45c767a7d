import 'package:dio/dio.dart';
import '../constants/api_constants.dart';
import '../models/api_response.dart';
import '../models/user_statistics.dart';
import 'api_service.dart';

class UserStatisticsService {
  static final UserStatisticsService _instance = UserStatisticsService._internal();
  factory UserStatisticsService() => _instance;
  UserStatisticsService._internal();

  final ApiService _apiService = ApiService();

  /// Get user statistics including donations, charities, and events
  Future<ApiResponse<UserStatistics>> getUserStatistics() async {
    try {
      final response = await _apiService.get(ApiConstants.userStatisticsEndpoint);
      
      final apiResponse = ApiResponse.fromJson(response.data);
      
      if (apiResponse.isSuccess && apiResponse.data != null) {
        final userStatistics = UserStatistics.fromJson(apiResponse.data);
        return ApiResponse<UserStatistics>(
          status: apiResponse.status,
          message: apiResponse.message,
          data: userStatistics,
        );
      } else {
        return ApiResponse<UserStatistics>(
          status: apiResponse.status,
          message: apiResponse.message,
          data: null,
        );
      }
    } on DioException catch (e) {
      return ApiResponse<UserStatistics>(
        status: false,
        message: _handleDioError(e),
        data: null,
      );
    } catch (e) {
      return ApiResponse<UserStatistics>(
        status: false,
        message: 'An unexpected error occurred. Please try again.',
        data: null,
      );
    }
  }

  /// Handle Dio errors and extract meaningful error messages
  String _handleDioError(DioException e) {
    String errorMessage = 'An unexpected error occurred. Please try again.';

    if (e.response != null) {
      final responseData = e.response!.data;

      // Try to extract error message from API response
      if (responseData is Map<String, dynamic>) {
        if (responseData.containsKey('message')) {
          errorMessage = responseData['message'];
        } else if (responseData.containsKey('error')) {
          errorMessage = responseData['error'];
        } else {
          // Handle different status codes
          switch (e.response!.statusCode) {
            case 401:
              errorMessage = 'Authentication required. Please log in again.';
              break;
            case 403:
              errorMessage = 'Access denied. You don\'t have permission to view this data.';
              break;
            case 404:
              errorMessage = 'Statistics not found.';
              break;
            case 500:
              errorMessage = 'Server error. Please try again later.';
              break;
            default:
              errorMessage = 'Server error (${e.response!.statusCode}). Please try again.';
          }
        }
      }
    } else {
      // Handle network errors
      switch (e.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          errorMessage = 'Connection timeout. Please check your internet connection.';
          break;
        case DioExceptionType.connectionError:
          errorMessage = 'Connection failed. Please check your internet connection.';
          break;
        case DioExceptionType.cancel:
          errorMessage = 'Request was cancelled.';
          break;
        default:
          errorMessage = 'Network error. Please check your connection and try again.';
      }
    }

    return errorMessage;
  }
}
