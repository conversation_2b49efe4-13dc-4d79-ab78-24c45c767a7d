import '../constants/api_constants.dart';
import '../models/api_response.dart';
import 'api_service.dart';
import 'token_service.dart';

class UserUpdateService {
  static final UserUpdateService _instance = UserUpdateService._internal();
  factory UserUpdateService() => _instance;
  UserUpdateService._internal();

  final ApiService _apiService = ApiService();

  /// Update user profile information
  Future<ApiResponse<Map<String, dynamic>>> updateUserProfile({
    String? name,
    String? address,
    String? city,
    String? country,
  }) async {
    try {
      // Build the payload with only non-null values
      final Map<String, dynamic> payload = {};
      
      if (name != null && name.isNotEmpty) payload['name'] = name;
      if (address != null && address.isNotEmpty) payload['address'] = address;
      if (city != null && city.isNotEmpty) payload['city'] = city;
      if (country != null && country.isNotEmpty) payload['country'] = country;

      // If no fields to update, return error
      if (payload.isEmpty) {
        return ApiResponse<Map<String, dynamic>>(
          status: false,
          message: 'No fields to update',
          data: null,
        );
      }

      final response = await _apiService.patch(
        ApiConstants.userUpdateEndpoint,
        data: payload,
      );
      
      final apiResponse = ApiResponse.fromJson(response.data);
      
      if (apiResponse.isSuccess && apiResponse.data != null) {
        final data = apiResponse.data as Map<String, dynamic>;
        
        // Update local storage with new user data
        if (data.containsKey('user')) {
          await TokenService.updateUserData(data['user']);
        }
        
        return ApiResponse<Map<String, dynamic>>(
          status: apiResponse.status,
          message: apiResponse.message,
          data: data,
        );
      } else {
        return ApiResponse<Map<String, dynamic>>(
          status: apiResponse.status,
          message: apiResponse.message,
          data: null,
        );
      }
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>(
        status: false,
        message: 'Failed to update profile: ${e.toString()}',
        data: null,
      );
    }
  }
}
