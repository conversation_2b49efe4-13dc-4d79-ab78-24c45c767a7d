import 'package:flutter/material.dart';
import 'package:islamic_charity_app/l10n/app_localizations.dart';
import '../../../../core/services/payment_service.dart';
import '../../../../core/utils/number_formatter.dart';
import '../../../../routes/app_routes.dart';

class DonatePage extends StatefulWidget {
  final String? campaignId;
  final String? campaignTitle;

  const DonatePage({
    super.key,
    this.campaignId,
    this.campaignTitle,
  });

  @override
  State<DonatePage> createState() => _DonatePageState();
}

class _DonatePageState extends State<DonatePage> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  String _selectedDonationType = 'General';
  bool _isAnonymous = false;
  bool _isProcessing = false;
  final PaymentService _paymentService = PaymentService();

  final List<String> _donationTypes = [
    'General',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  ];

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  /// Calculate total amount including processing fee
  double _calculateTotalAmount(double baseAmount) {
    final processingFee = baseAmount * 0.025; // 2.5%
    return baseAmount + processingFee;
  }

  /// Build amount breakdown widget
  Widget _buildAmountBreakdown() {
    final baseAmount = double.parse(_amountController.text);
    final processingFee = baseAmount * 0.025;
    final totalAmount = baseAmount + processingFee;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Donation Amount:',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              Text(
                NumberFormatter.formatAmountWithCurrency(baseAmount),
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Processing Fee (2.5%):',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              Text(
                NumberFormatter.formatAmountWithCurrency(processingFee),
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
          const Divider(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Amount:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                NumberFormatter.formatAmountWithCurrency(totalAmount),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          l10n.donate,
          style: const TextStyle(color: Colors.white),
        ),
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            if (widget.campaignId != null) ...[
              _buildCampaignSection(l10n),
              const SizedBox(height: 24),
            ],
            _buildDonationTypeSection(l10n),
            const SizedBox(height: 24),
            _buildAmountSection(l10n),
            const SizedBox(height: 24),
            _buildAnonymousToggle(l10n),
            const SizedBox(height: 32),
            _buildDonateButton(l10n),
            const SizedBox(height: 80), // Extra bottom space to push button up from bottom nav
          ],
        ),
      ),
    );
  }

  Widget _buildCampaignSection(AppLocalizations l10n) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.campaign,
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Donating to Campaign',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            widget.campaignTitle ?? 'Campaign',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Your donation will go directly to this campaign',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDonationTypeSection(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.donationType,
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<String>(
          value: _selectedDonationType,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          items: _donationTypes.map((String type) {
            return DropdownMenuItem<String>(
              value: type,
              child: Text(type),
            );
          }).toList(),
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                _selectedDonationType = newValue;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildAmountSection(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.donationAmount,
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _amountController,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          decoration: InputDecoration(
            labelText: l10n.amount,
            prefixText: 'SEK ',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return l10n.pleaseEnterAmount;
            }
            
            // Check if it matches the required format (XX.XX)
            final regex = RegExp(r'^\d+\.\d{2}$');
            if (!regex.hasMatch(value)) {
              return 'Please enter amount in format: XX.XX (e.g., 50.00)';
            }
            
            if (double.tryParse(value) == null) {
              return l10n.pleaseEnterValidAmount;
            }
            return null;
          },
          onChanged: (value) {
            setState(() {});
          },
        ),
        const SizedBox(height: 8),
        Text(
          '2.5% processing fee will also incur',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
            fontStyle: FontStyle.italic,
          ),
        ),
        if (_amountController.text.isNotEmpty && double.tryParse(_amountController.text) != null) ...[
          const SizedBox(height: 8),
          _buildAmountBreakdown(),
        ],
        const SizedBox(height: 16),
        Text(
          'Suggested amounts:',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildAmountChip('10.00'),
            _buildAmountChip('25.00'),
            _buildAmountChip('50.00'),
            _buildAmountChip('100.00'),
            _buildAmountChip('500.00'),
            _buildAmountChip('1000.00'),
          ],
        ),
      ],
    );
  }

  Widget _buildAmountChip(String amount) {
    return ActionChip(
      label: Text('SEK $amount'),
      onPressed: () {
        _amountController.text = amount;
        setState(() {});
      },
      backgroundColor: _amountController.text == amount
          ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
          : null,
    );
  }

  Widget _buildAnonymousToggle(AppLocalizations l10n) {
    return SwitchListTile(
      title: Text(l10n.anonymousDonation),
      subtitle: Text(l10n.anonymousDonationDescription),
      value: _isAnonymous,
      onChanged: (bool value) {
        setState(() {
          _isAnonymous = value;
        });
      },
    );
  }

  Widget _buildDonateButton(AppLocalizations l10n) {
    return ElevatedButton(
      onPressed: _isProcessing ? null : _submitDonation,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      child: _isProcessing
          ? const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Text(l10n.donateNow),
    );
  }

  void _submitDonation() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isProcessing = true;
      });

      try {
        final baseAmount = double.parse(_amountController.text);
        final totalAmount = _calculateTotalAmount(baseAmount);
        final donationTypeForApi = _selectedDonationType;

        // Create payment
        final paymentResponse = await _paymentService.createPayment(
          amount: totalAmount,
          transactionReference: '${_selectedDonationType.toLowerCase()}_${DateTime.now().millisecondsSinceEpoch}',
          type: donationTypeForApi,
          isAnonymous: _isAnonymous,
          campaignId: widget.campaignId,
        );

        if (paymentResponse.isSuccess && paymentResponse.transactionId != null) {
          // Navigate to payment webview
          if (mounted) {
            Navigator.pushNamed(
              context,
              AppRoutes.paymentWebview,
              arguments: {
                'transactionId': paymentResponse.transactionId!,
                'amount': totalAmount,
                'donationType': _selectedDonationType,
              },
            );
          }
        } else {
          // Show error message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(paymentResponse.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isProcessing = false;
          });
        }
      }
    }
  }
}
