import 'package:flutter/material.dart';
import 'package:islamic_charity_app/core/models/transaction.dart';
import 'package:provider/provider.dart';
import '../../../../core/providers/language_provider.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/services/token_service.dart';
import '../../../../core/services/user_statistics_service.dart';
import '../../../../core/services/donations_service.dart';
import '../../../../core/models/user_statistics.dart';
import '../../../../core/utils/number_formatter.dart';


import 'package:islamic_charity_app/l10n/app_localizations.dart';
import 'package:islamic_charity_app/routes/app_routes.dart';
import 'package:islamic_charity_app/features/subscription/presentation/pages/subscription_page.dart';
import 'edit_profile_page.dart';

class ProfilePage extends StatefulWidget {
  final bool showAppBar;

  const ProfilePage({super.key, this.showAppBar = true});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  Map<String, dynamic>? _userData;
  UserStatistics? _userStatistics;
  List<Transaction> _donations = [];
  bool _isLoading = true;
  bool _isStatisticsLoading = true;
  bool _isDonationsLoading = true;
  bool _isLoadingMoreDonations = false;
  String? _statisticsError;
  String? _donationsError;
  int _currentPage = 1;
  bool _hasMoreDonations = true;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadUserData();
    _loadUserStatistics();
    _loadDonations();
    _scrollController.addListener(_onScroll);
  }

  DateTime _getCurrentMonthStartDate() {
    final now = DateTime.now();
    return DateTime(now.year, now.month, 1);
  }

  DateTime _getCurrentMonthEndDate() {
    final now = DateTime.now();
    return DateTime(now.year, now.month + 1, 0);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    try {
      final userData = await TokenService.getUserData();
      setState(() {
        _userData = userData;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadUserStatistics() async {
    try {
      final response = await UserStatisticsService().getUserStatistics();
      setState(() {
        if (response.isSuccess && response.data != null) {
          _userStatistics = response.data;
          _statisticsError = null;
        } else {
          _statisticsError = response.message;
        }
        _isStatisticsLoading = false;
      });
    } catch (e) {
      setState(() {
        _statisticsError = 'Failed to load statistics: ${e.toString()}';
        _isStatisticsLoading = false;
      });
    }
  }

  Future<void> _loadDonations({bool isRefresh = false}) async {
    if (isRefresh) {
      setState(() {
        _currentPage = 1;
        _donations.clear();
        _isDonationsLoading = true;
        _donationsError = null;
        _hasMoreDonations = true;
      });
    }

    try {
      final response = await DonationsService().getUserDonationsPaginated(
        page: _currentPage,
        limit: 10,
        startDate: _getCurrentMonthStartDate(),
        endDate: _getCurrentMonthEndDate(),
      );

      setState(() {
        if (response.isSuccess && response.data != null) {
          final transactionsPaginated = response.data!;
          if (isRefresh) {
            _donations = transactionsPaginated.donations;
          } else {
            _donations.addAll(transactionsPaginated.donations);
          }
          _hasMoreDonations = transactionsPaginated.hasMorePages;
          _donationsError = null;
        } else {
          _donationsError = response.message;
        }
        _isDonationsLoading = false;
        _isLoadingMoreDonations = false;
      });
    } catch (e) {
      setState(() {
        _donationsError = 'Failed to load donations: ${e.toString()}';
        _isDonationsLoading = false;
        _isLoadingMoreDonations = false;
      });
    }
  }

  Future<void> _loadMoreDonations() async {
    if (_isLoadingMoreDonations || !_hasMoreDonations) return;

    setState(() {
      _isLoadingMoreDonations = true;
      _currentPage++;
    });

    await _loadDonations();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      _loadMoreDonations();
    }
  }

  Future<void> _logout(BuildContext context) async {
    await AuthService().logout();
    if (context.mounted) {
      Navigator.pushNamedAndRemoveUntil(
        context,
        AppRoutes.login,
        (route) => false,
      );
    }
  }

  Future<void> _navigateToEditProfile(BuildContext context) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const EditProfilePage(),
      ),
    );

    // If profile was updated successfully, refresh the data
    if (result == true) {
      _loadUserData();
      _loadUserStatistics();
      _loadDonations(isRefresh: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: widget.showAppBar ? AppBar(
        title: Text(
          l10n.profile,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
      ) : null,
      body: Column(
        children: [
          // Header section with gradient background
          Container(
            width: double.infinity,
            padding: EdgeInsets.only(
              top: widget.showAppBar ? 24 : MediaQuery.of(context).padding.top + 16,
              left: 24,
              right: 24,
              bottom: 24,
            ),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.primary.withOpacity(0.8),
                ],
              ),
            ),
            child: widget.showAppBar ? const SizedBox.shrink() : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  l10n.profile,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),

              ],
            ),
          ),
          // Scrollable content
          Expanded(
            child: _isLoading
                ? const Center(
                    child: Padding(
                      padding: EdgeInsets.all(32),
                      child: CircularProgressIndicator(),
                    ),
                  )
                : SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildProfileHeader(context),
                        const SizedBox(height: 24),
                        _buildStatistics(context),
                        const SizedBox(height: 24),
                        _buildSectionTitle(context, 'My Activities (This Month)'),
                        const SizedBox(height: 16),
                        _buildActivitySection(context),
                        const SizedBox(height: 16),
                        _buildAccountSection(context),
                      ],
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileHeader(BuildContext context) {
    final name = _userData?['name'] ?? 'User';
    final email = _userData?['email'] ?? '<EMAIL>';
    final phone = _userData?['phone'] ?? '';

    return Row(
      children: [
        CircleAvatar(
          radius: 40,
          backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
          child: Text(
            name.isNotEmpty ? name[0].toUpperCase() : 'U',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                name,
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 4),
              Text(
                email,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              if (phone.isNotEmpty) ...[
                const SizedBox(height: 2),
                Text(
                  phone,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
              const SizedBox(height: 8),
              OutlinedButton(
                onPressed: () => _navigateToEditProfile(context),
                child: const Text('Edit Profile'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatistics(BuildContext context) {
    if (_isStatisticsLoading) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.withOpacity(0.3)),
        ),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_statisticsError != null) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.red.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Icon(Icons.error, color: Colors.red, size: 32),
            const SizedBox(height: 8),
            Text(
              'Failed to load statistics',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              _statisticsError!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.red[700],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _loadUserStatistics,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Theme.of(context).colorScheme.primary.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Donation Statistics',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'LIVE',
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 120,
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                _buildStatItem(
                  context,
                  Icons.volunteer_activism,
                  'Total Donations',
                  _userStatistics != null
                    ? NumberFormatter.formatAmountWithCurrency(_userStatistics!.totalDonationsAmount)
                    : NumberFormatter.formatAmountWithCurrency(0.0),
                  isLocked: false,
                ),
                const SizedBox(width: 12),
                _buildStatItem(
                  context,
                  Icons.event,
                  'Events Attended',
                  '${_userStatistics?.totalEvents ?? 0}',
                  isLocked: false,
                ),
                const SizedBox(width: 12),
                _buildStatItem(
                  context,
                  Icons.people,
                  'Charities Supported',
                  '${_userStatistics?.totalCharities ?? 0}',
                  isLocked: false,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    IconData icon,
    String label,
    String value, {
    bool isLocked = false,
  }) {
    return Container(
      width: 140,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withOpacity(0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Stack(
            children: [
              Icon(
                icon,
                color: isLocked ? Colors.grey : Theme.of(context).colorScheme.primary,
                size: 24,
              ),
              if (isLocked)
                Positioned(
                  right: -2,
                  top: -2,
                  child: Container(
                    padding: const EdgeInsets.all(1),
                    decoration: const BoxDecoration(
                      color: Colors.orange,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(Icons.schedule, size: 6, color: Colors.white),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: isLocked ? Colors.grey : Theme.of(context).colorScheme.primary,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
              fontSize: 11,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium,
    );
  }

  Widget _buildActivitySection(BuildContext context) {
    if (_isDonationsLoading) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.withOpacity(0.3)),
        ),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_donationsError != null) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.red.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Icon(Icons.error, color: Colors.red, size: 32),
            const SizedBox(height: 8),
            Text(
              'Failed to load activity',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () => _loadDonations(isRefresh: true),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }



    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Theme.of(context).colorScheme.primary.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Activity',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'LIVE',
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          // Fixed height container with scrollable content
          SizedBox(
            height: 280, // Fixed height for the activity section
            child: _donations.isEmpty
                ? _buildEmptyActivityCard(context)
                : ListView.builder(
                    controller: _scrollController,
                    padding: EdgeInsets.zero,
                    itemCount: _donations.length + (_hasMoreDonations ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index == _donations.length) {
                        // Loading indicator at the bottom
                        return _isLoadingMoreDonations
                            ? const Padding(
                                padding: EdgeInsets.all(16),
                                child: Center(
                                  child: CircularProgressIndicator(),
                                ),
                              )
                            : const SizedBox.shrink();
                      }
                      return _buildDonationCard(context, _donations[index]);
                    },
                  ),
          ),
        ],
      ),
    );
  }



  Widget _buildDonationCard(BuildContext context, Transaction donation) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Donation type at the top
                      Text(
                        donation.type,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      // ID below in small text
                      Text(
                        'ID: ${donation.id.substring(0, 8)}...',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 4),
                      // Campaign name if available
                      if (donation.campaign != null) ...[
                        Row(
                          children: [
                            Icon(
                              Icons.campaign,
                              size: 14,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                donation.campaign!.name,
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Colors.grey[600],
                                  fontStyle: FontStyle.italic,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                      ],
                      // Date and time at the bottom
                      Text(
                        donation.formattedDateTime,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                ),
                // Amount on the top right
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      donation.formattedAmount,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.green[700],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: donation.status == 'CAPTURED' ? Colors.green : Colors.orange,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        donation.status,
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            if (donation.isAnonymous) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.visibility_off,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Anonymous donation',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyActivityCard(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.volunteer_activism,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 24),
            Text(
              'No donations this month',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Your donation history for this month will appear here once you make your first donation.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountSection(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final languageProvider = Provider.of<LanguageProvider>(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.account,
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 16),
        _buildAccountCard(
          context,
          Icons.card_membership,
          l10n.subscription,
          'Manage your subscription plan',
          () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const SubscriptionPage(),
              ),
            );
          },
        ),
        _buildAccountCard(
          context,
          Icons.language,
          l10n.language,
          'Change app language',
          () {
            _showLanguageDialog(context, languageProvider);
          },
        ),
        _buildAccountCard(
          context,
          Icons.notifications,
          l10n.notifications,
          'Manage notification settings',
          () {
            Navigator.pushNamed(context, AppRoutes.notifications);
          },
        ),
        _buildAccountCard(
          context,
          Icons.help,
          l10n.help,
          'Get help and support',
          () {
            _showComingSoonDialog(context, 'Help & Support');
          },
        ),
        _buildAccountCard(
          context,
          Icons.info,
          l10n.about,
          'About the app',
          () {
            Navigator.pushNamed(context, AppRoutes.about);
          },
        ),
        _buildAccountCard(
          context,
          Icons.logout,
          l10n.logout,
          'Sign out of your account',
          () => _logout(context),
          isDestructive: true,
        ),
      ],
    );
  }

  Widget _buildAccountCard(
    BuildContext context,
    IconData icon,
    String title,
    String subtitle,
    VoidCallback onTap, {
    bool isDestructive = false,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          icon,
          color: isDestructive
              ? Colors.red
              : Theme.of(context).colorScheme.primary,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isDestructive ? Colors.red : null,
          ),
        ),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.chevron_right),
        onTap: onTap,
      ),
    );
  }

  void _showLanguageDialog(
      BuildContext context, LanguageProvider languageProvider) {
    final l10n = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.selectLanguage),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: Text(l10n.english),
              onTap: () {
                languageProvider.setLanguage('en');
                Navigator.pop(context);
              },
            ),
            ListTile(
              title: Text(l10n.swedish),
              onTap: () {
                languageProvider.setLanguage('sv');
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showComingSoonDialog(BuildContext context, String featureName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: const Icon(
          Icons.construction,
          size: 48,
          color: Colors.orange,
        ),
        title: const Text('Coming Soon!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '$featureName is currently under development.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'We\'re working hard to bring you this feature soon!',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it!'),
          ),
        ],
      ),
    );
  }
}