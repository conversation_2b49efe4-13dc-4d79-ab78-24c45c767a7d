// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Islamic Charity';

  @override
  String get home => 'Home';

  @override
  String get campaigns => 'Campaigns';

  @override
  String get events => 'Events';

  @override
  String get profile => 'Profile';

  @override
  String get donate => 'Donate';

  @override
  String get zakat => 'Zakat';

  @override
  String get charities => 'Charities';

  @override
  String get search => 'Search';

  @override
  String get language => 'Language';

  @override
  String get settings => 'Settings';

  @override
  String get account => 'Account';

  @override
  String get subscription => 'Subscription';

  @override
  String get notifications => 'Notifications';

  @override
  String get help => 'Help';

  @override
  String get about => 'About';

  @override
  String get logout => 'Logout';

  @override
  String get english => 'English';

  @override
  String get swedish => 'Swedish';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get quickActions => 'Quick Actions';

  @override
  String get recentCampaigns => 'Recent Campaigns';

  @override
  String get viewAll => 'View All';

  @override
  String get raised => 'Raised';

  @override
  String get goal => 'Goal';

  @override
  String get daysLeft => 'Days Left';

  @override
  String get donateNow => 'Donate Now';

  @override
  String get campaignDetails => 'Campaign Details';

  @override
  String get eventDetails => 'Event Details';

  @override
  String get registerNow => 'Register Now';

  @override
  String get shareEvent => 'Share Event';

  @override
  String get upcoming => 'Upcoming';

  @override
  String get past => 'Past';

  @override
  String get online => 'Online';

  @override
  String get inPerson => 'In Person';

  @override
  String get all => 'All';

  @override
  String get newEvent => 'New Event';

  @override
  String get peopleAttending => 'people attending';

  @override
  String get searchEvents => 'Search events...';

  @override
  String get searchCampaigns => 'Search campaigns...';

  @override
  String get newCampaign => 'New Campaign';

  @override
  String get createCampaign => 'Create Campaign';

  @override
  String get create => 'Create';

  @override
  String get campaignTitle => 'Campaign Title';

  @override
  String get pleaseEnterTitle => 'Please enter a title';

  @override
  String get category => 'Category';

  @override
  String get description => 'Description';

  @override
  String get pleaseEnterDescription => 'Please enter a description';

  @override
  String get goalAmount => 'Goal Amount';

  @override
  String get pleaseEnterGoalAmount => 'Please enter a goal amount';

  @override
  String get pleaseEnterValidAmount => 'Please enter a valid amount';

  @override
  String get endDate => 'End Date';

  @override
  String get selectEndDate => 'Select End Date';

  @override
  String get zakatCalculator => 'Zakat Calculator';

  @override
  String get enterYourAssets => 'Enter Your Assets';

  @override
  String get cashInHand => 'Cash in Hand';

  @override
  String get goldValue => 'Gold Value';

  @override
  String get silverValue => 'Silver Value';

  @override
  String get stocksValue => 'Stocks Value';

  @override
  String get businessValue => 'Business Value';

  @override
  String get enterYourLiabilities => 'Enter Your Liabilities';

  @override
  String get debts => 'Debts';

  @override
  String get calculateZakat => 'Calculate Zakat';

  @override
  String get yourZakatAmount => 'Your Zakat Amount';

  @override
  String get zakatPercentage => 'This is 2.5% of your net worth';

  @override
  String get donationType => 'Donation Type';

  @override
  String get donationAmount => 'Donation Amount';

  @override
  String get amount => 'Amount';

  @override
  String get pleaseEnterAmount => 'Please enter an amount';

  @override
  String get paymentMethod => 'Payment Method';

  @override
  String get anonymousDonation => 'Anonymous Donation';

  @override
  String get anonymousDonationDescription => 'Your name will not be displayed publicly';

  @override
  String get ourMission => 'Our Mission';

  @override
  String get missionDescription => 'We are dedicated to serving humanity through charitable initiatives, promoting social welfare, and fostering community development in accordance with Islamic principles.';

  @override
  String get ourValues => 'Our Values';

  @override
  String get compassion => 'Compassion - Showing empathy and care for those in need';

  @override
  String get integrity => 'Integrity - Maintaining honesty and ethical standards';

  @override
  String get community => 'Community - Building strong, supportive relationships';

  @override
  String get transparency => 'Transparency - Ensuring clear and open communication';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get donationUpdates => 'Donation Updates';

  @override
  String get campaignUpdates => 'Campaign Updates';

  @override
  String get systemNotifications => 'System Notifications';

  @override
  String get markAsRead => 'Mark as read';

  @override
  String get deleteNotification => 'Delete notification';

  @override
  String get featuredCampaigns => 'Featured Campaigns';

  @override
  String get topCharities => 'Top Charities';

  @override
  String get upcomingEvents => 'Upcoming Events';
}
