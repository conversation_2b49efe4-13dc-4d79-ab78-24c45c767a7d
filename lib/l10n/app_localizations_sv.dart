// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Swedish (`sv`).
class AppLocalizationsSv extends AppLocalizations {
  AppLocalizationsSv([String locale = 'sv']) : super(locale);

  @override
  String get appTitle => 'Islamisk välgörenhet';

  @override
  String get home => 'Hem';

  @override
  String get campaigns => 'Kampanjer';

  @override
  String get events => 'Evenemang';

  @override
  String get profile => 'Profil';

  @override
  String get donate => 'Donera';

  @override
  String get zakat => 'Zakat';

  @override
  String get charities => 'Topporganisationer';

  @override
  String get search => 'Sök';

  @override
  String get language => 'Språk';

  @override
  String get settings => 'Inställningar';

  @override
  String get account => 'Konto';

  @override
  String get subscription => 'Prenumeration';

  @override
  String get notifications => 'Notifikationer';

  @override
  String get help => 'Hjälp';

  @override
  String get about => 'Om oss';

  @override
  String get logout => 'Logga ut';

  @override
  String get english => 'Engelska';

  @override
  String get swedish => 'Svenska';

  @override
  String get selectLanguage => 'Välj Språk';

  @override
  String get quickActions => 'Snabbåtgärder';

  @override
  String get recentCampaigns => 'Senaste kampanjer';

  @override
  String get viewAll => 'Visa alla';

  @override
  String get raised => 'Insamlat';

  @override
  String get goal => 'Mål';

  @override
  String get daysLeft => 'Dagar kvar';

  @override
  String get donateNow => 'Donera nu';

  @override
  String get campaignDetails => 'Kampanjdetaljer';

  @override
  String get eventDetails => 'Evenemangsdetaljer';

  @override
  String get registerNow => 'Registrera Nu';

  @override
  String get shareEvent => 'Dela Evenemang';

  @override
  String get upcoming => 'Kommande evenemang';

  @override
  String get past => 'Tidigare';

  @override
  String get online => 'Online';

  @override
  String get inPerson => 'På Plats';

  @override
  String get all => 'Alla';

  @override
  String get newEvent => 'Nytt Evenemang';

  @override
  String get peopleAttending => 'personer deltar';

  @override
  String get searchEvents => 'Sök evenemang...';

  @override
  String get searchCampaigns => 'Sök kampanjer...';

  @override
  String get newCampaign => 'Ny kampanj';

  @override
  String get createCampaign => 'Skapa kampanj';

  @override
  String get create => 'Skapa';

  @override
  String get campaignTitle => 'Kampanjtitel';

  @override
  String get pleaseEnterTitle => 'Vänligen ange en titel';

  @override
  String get category => 'Kategori';

  @override
  String get description => 'Beskrivning';

  @override
  String get pleaseEnterDescription => 'Vänligen ange en beskrivning';

  @override
  String get goalAmount => 'Målbelopp';

  @override
  String get pleaseEnterGoalAmount => 'Vänligen ange ett målbelopp';

  @override
  String get pleaseEnterValidAmount => 'Vänligen ange ett giltigt belopp';

  @override
  String get endDate => 'Slutdatum';

  @override
  String get selectEndDate => 'Välj slutdatum';

  @override
  String get zakatCalculator => 'Zakat-kalkylator';

  @override
  String get enterYourAssets => 'Ange dina tillgångar';

  @override
  String get cashInHand => 'Kontanter';

  @override
  String get goldValue => 'Guldvärde';

  @override
  String get silverValue => 'Silvervärde';

  @override
  String get stocksValue => 'Aktievärde';

  @override
  String get businessValue => 'Företagsvärde';

  @override
  String get enterYourLiabilities => 'Ange dina skulder';

  @override
  String get debts => 'Skulder';

  @override
  String get calculateZakat => 'Beräkna Zakat';

  @override
  String get yourZakatAmount => 'Ditt Zakat-belopp';

  @override
  String get zakatPercentage => 'Detta är 2,5% av ditt nettovärde';

  @override
  String get donationType => 'Donationstyp';

  @override
  String get donationAmount => 'Donationsbelopp';

  @override
  String get amount => 'Belopp';

  @override
  String get pleaseEnterAmount => 'Vänligen ange ett belopp';

  @override
  String get paymentMethod => 'Betalningsmetod';

  @override
  String get anonymousDonation => 'Anonym donation';

  @override
  String get anonymousDonationDescription => 'Ditt namn kommer inte att visas offentligt';

  @override
  String get ourMission => 'Vårt uppdrag';

  @override
  String get missionDescription => 'Vi är dedikerade till att tjäna mänskligheten genom välgörenhetsinitiativ, främja social välfärd och stödja samhällsutveckling i enlighet med islamiska principer.';

  @override
  String get ourValues => 'Våra värderingar';

  @override
  String get compassion => 'Medkänsla - Visar empati och omsorg för de behövande';

  @override
  String get integrity => 'Integritet - Upprätthåller ärlighet och etiska standarder';

  @override
  String get community => 'Gemenskap - Bygger starka, stödjande relationer';

  @override
  String get transparency => 'Transparens - Säkerställer tydlig och öppen kommunikation';

  @override
  String get contactUs => 'Kontakta oss';

  @override
  String get donationUpdates => 'Donationsuppdateringar';

  @override
  String get campaignUpdates => 'Kampanjuppdateringar';

  @override
  String get systemNotifications => 'Systemnotifikationer';

  @override
  String get markAsRead => 'Markera som läst';

  @override
  String get deleteNotification => 'Ta bort notifikation';

  @override
  String get featuredCampaigns => 'Utvalda kampanjer';

  @override
  String get topCharities => 'Topporganisationer';

  @override
  String get upcomingEvents => 'Kommande evenemang';
}
